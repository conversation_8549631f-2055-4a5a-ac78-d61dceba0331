<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新认证系统测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
            width: 200px;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🔐 新认证系统测试</h1>
    
    <div class="container">
        <h2>系统状态</h2>
        <div id="system-status"></div>
        <button onclick="checkSystemStatus()">检查系统状态</button>
    </div>
    
    <div class="container">
        <h2>登录测试</h2>
        <div>
            <input type="email" id="test-email" placeholder="邮箱" value="<EMAIL>">
            <input type="password" id="test-password" placeholder="密码" value="123456">
            <button onclick="testLogin()">测试登录</button>
            <button onclick="testLogout()">登出</button>
        </div>
        <div id="login-status"></div>
    </div>
    
    <div class="container">
        <h2>权限测试</h2>
        <div id="permission-status"></div>
        <button onclick="testPermissions()">测试权限</button>
    </div>
    
    <div class="container">
        <h2>详细日志</h2>
        <div id="logs"></div>
        <button onclick="clearLogs()">清除日志</button>
    </div>

    <!-- 加载认证系统 -->
    <script src="supabase-simple.js"></script>
    <script src="js/auth-system.js"></script>
    
    <script>
        let logContainer;
        
        document.addEventListener('DOMContentLoaded', function() {
            logContainer = document.getElementById('logs');
            log('页面加载完成');
            
            // 等待认证系统初始化
            setTimeout(() => {
                checkSystemStatus();
                testPermissions();
            }, 2000);
        });
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `status ${type}`;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }
        
        function checkSystemStatus() {
            const statusDiv = document.getElementById('system-status');
            let html = '';
            
            // 检查基础组件
            html += `<div class="status ${typeof window.supabaseClient !== 'undefined' ? 'success' : 'error'}">
                Supabase客户端: ${typeof window.supabaseClient !== 'undefined' ? '✅ 已加载' : '❌ 未加载'}
            </div>`;
            
            html += `<div class="status ${typeof window.authSystem !== 'undefined' ? 'success' : 'error'}">
                认证系统: ${typeof window.authSystem !== 'undefined' ? '✅ 已加载' : '❌ 未加载'}
            </div>`;
            
            // 检查全局函数
            const globalFunctions = ['loginUser', 'registerUser', 'logoutUser', 'canViewDetails', 'canDownload'];
            globalFunctions.forEach(func => {
                html += `<div class="status ${typeof window[func] === 'function' ? 'success' : 'error'}">
                    ${func}: ${typeof window[func] === 'function' ? '✅ 可用' : '❌ 不可用'}
                </div>`;
            });
            
            // 检查当前用户状态
            if (window.authSystem) {
                const currentUser = window.authSystem.getCurrentUser();
                const userType = window.authSystem.getUserType();
                
                html += `<div class="status info">
                    当前用户: ${currentUser ? currentUser.email : '未登录'}<br>
                    用户类型: ${userType}<br>
                    是否已登录: ${window.authSystem.isLoggedIn() ? '是' : '否'}
                </div>`;
            }
            
            statusDiv.innerHTML = html;
            log('系统状态检查完成');
        }
        
        async function testLogin() {
            const email = document.getElementById('test-email').value;
            const password = document.getElementById('test-password').value;
            const statusDiv = document.getElementById('login-status');
            
            if (!email || !password) {
                statusDiv.innerHTML = '<div class="status error">请输入邮箱和密码</div>';
                return;
            }
            
            try {
                log(`开始测试登录: ${email}`);
                statusDiv.innerHTML = '<div class="status info">正在登录...</div>';
                
                const result = await window.authSystem.loginUser(email, password);
                
                if (result.success) {
                    statusDiv.innerHTML = '<div class="status success">登录成功！</div>';
                    log(`登录成功: ${email}`, 'success');
                    
                    // 更新系统状态
                    setTimeout(checkSystemStatus, 500);
                    setTimeout(testPermissions, 500);
                } else {
                    statusDiv.innerHTML = `<div class="status error">登录失败: ${result.error}</div>`;
                    log(`登录失败: ${result.error}`, 'error');
                }
            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">登录错误: ${error.message}</div>`;
                log(`登录错误: ${error.message}`, 'error');
            }
        }
        
        function testLogout() {
            const statusDiv = document.getElementById('login-status');
            
            try {
                window.authSystem.logout();
                statusDiv.innerHTML = '<div class="status success">已登出</div>';
                log('用户已登出', 'success');
                
                // 更新系统状态
                setTimeout(checkSystemStatus, 500);
                setTimeout(testPermissions, 500);
            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">登出错误: ${error.message}</div>`;
                log(`登出错误: ${error.message}`, 'error');
            }
        }
        
        function testPermissions() {
            const statusDiv = document.getElementById('permission-status');
            let html = '';
            
            if (!window.authSystem) {
                html = '<div class="status error">认证系统未初始化</div>';
            } else {
                const permissions = [
                    { name: '查看详情', func: 'canViewDetails' },
                    { name: '基础下载', func: 'canDownloadBasic' },
                    { name: '完整下载', func: 'canDownloadAll' },
                    { name: '管理员权限', func: 'isAdmin' }
                ];
                
                permissions.forEach(perm => {
                    const hasPermission = window.authSystem[perm.func]();
                    html += `<div class="status ${hasPermission ? 'success' : 'info'}">
                        ${perm.name}: ${hasPermission ? '✅ 有权限' : '❌ 无权限'}
                    </div>`;
                });
            }
            
            statusDiv.innerHTML = html;
            log('权限测试完成');
        }
        
        function clearLogs() {
            logContainer.innerHTML = '';
            log('日志已清除');
        }
    </script>
</body>
</html>
