# Fix Python PATH issues by prioritizing the real Python installation

Write-Host "Fixing Python PATH priority..." -ForegroundColor Green

# Python installation paths
$pythonPath = "D:\Users\Administrator\AppData\Local\Programs\Python\Python313\"
$pythonScriptsPath = "D:\Users\Administrator\AppData\Local\Programs\Python\Python313\Scripts\"

# Get current user PATH
$currentUserPath = [Environment]::GetEnvironmentVariable("Path", [EnvironmentVariableTarget]::User)

# Remove Windows Store Python path if it exists
$windowsAppsPath = "C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps"
$pathsArray = $currentUserPath -split ';' | Where-Object { $_ -ne $windowsAppsPath -and $_ -ne "" }

# Add Python paths at the beginning (higher priority)
$newPathArray = @()
$newPathArray += $pythonPath
$newPathArray += $pythonScriptsPath
$newPathArray += $pathsArray | Where-Object { $_ -notlike "*Python*" }

# Join paths back together
$newPath = $newPathArray -join ';'

# Set the new PATH
[Environment]::SetEnvironmentVariable("Path", $newPath, [EnvironmentVariableTarget]::User)
Write-Host "Updated user PATH with Python priority" -ForegroundColor Green

# Update current session
$env:Path = $newPath + ";" + [Environment]::GetEnvironmentVariable("Path", [EnvironmentVariableTarget]::Machine)

# Test Python command
Write-Host "`nTesting Python command..." -ForegroundColor Yellow
try {
    $pythonCmd = Get-Command python -ErrorAction SilentlyContinue
    if ($pythonCmd) {
        Write-Host "Python found at: $($pythonCmd.Source)" -ForegroundColor Green
        $version = & python --version 2>&1
        Write-Host "Version: $version" -ForegroundColor Green
    } else {
        Write-Host "Python command not found" -ForegroundColor Red
    }
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test pip command
Write-Host "`nTesting pip command..." -ForegroundColor Yellow
try {
    $pipCmd = Get-Command pip -ErrorAction SilentlyContinue
    if ($pipCmd) {
        Write-Host "pip found at: $($pipCmd.Source)" -ForegroundColor Green
        $version = & pip --version 2>&1
        Write-Host "Version: $version" -ForegroundColor Green
    } else {
        Write-Host "pip command not found" -ForegroundColor Red
    }
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nPython PATH fix completed!" -ForegroundColor Green
Write-Host "Final user PATH:" -ForegroundColor Cyan
$finalPath = [Environment]::GetEnvironmentVariable("Path", [EnvironmentVariableTarget]::User)
$finalPath -split ';' | ForEach-Object { 
    if ($_ -like "*Python*") {
        Write-Host "  [PYTHON] $_" -ForegroundColor Green
    } else {
        Write-Host "           $_" -ForegroundColor Gray
    }
}
