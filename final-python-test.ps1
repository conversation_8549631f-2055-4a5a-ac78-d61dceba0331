# Final Python Environment Test

Write-Host "=== Final Python Environment Test ===" -ForegroundColor Green

# Test 1: Direct python command
Write-Host "`n1. Testing python command:" -ForegroundColor Yellow
$pythonResult = & python --version 2>&1
if ($LASTEXITCODE -eq 0) {
    Write-Host "   ✅ SUCCESS: $pythonResult" -ForegroundColor Green
} else {
    Write-Host "   ❌ FAILED: $pythonResult" -ForegroundColor Red
}

# Test 2: Direct pip command
Write-Host "`n2. Testing pip command:" -ForegroundColor Yellow
$pipResult = & pip --version 2>&1
if ($LASTEXITCODE -eq 0) {
    Write-Host "   ✅ SUCCESS: $pipResult" -ForegroundColor Green
} else {
    Write-Host "   ❌ FAILED: $pipResult" -ForegroundColor Red
}

# Test 3: Python module test
Write-Host "`n3. Testing Python http.server module:" -ForegroundColor Yellow
$moduleTest = & python -c "import http.server; print('http.server module available')" 2>&1
if ($LASTEXITCODE -eq 0) {
    Write-Host "   ✅ SUCCESS: $moduleTest" -ForegroundColor Green
} else {
    Write-Host "   ❌ FAILED: $moduleTest" -ForegroundColor Red
}

# Test 4: Show Python location
Write-Host "`n4. Python executable location:" -ForegroundColor Yellow
$pythonWhere = & where python 2>&1
Write-Host "   $pythonWhere" -ForegroundColor Cyan

# Test 5: Show pip location
Write-Host "`n5. Pip executable location:" -ForegroundColor Yellow
$pipWhere = & where pip 2>&1
Write-Host "   $pipWhere" -ForegroundColor Cyan

Write-Host "`n=== Test Summary ===" -ForegroundColor Green
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Python environment is properly configured!" -ForegroundColor Green
    Write-Host "You can now use:" -ForegroundColor White
    Write-Host "  - python --version" -ForegroundColor Cyan
    Write-Host "  - pip install <package>" -ForegroundColor Cyan
    Write-Host "  - python -m http.server 8080" -ForegroundColor Cyan
} else {
    Write-Host "❌ Some issues detected. Please restart your terminal." -ForegroundColor Red
}

Write-Host "`n=== Quick Start Commands ===" -ForegroundColor Yellow
Write-Host "To start HTTP server: python -m http.server 8080" -ForegroundColor White
Write-Host "To install packages: pip install <package-name>" -ForegroundColor White
Write-Host "To check versions: python --version && pip --version" -ForegroundColor White
