// Supabase配置文件
// 这个文件提供Supabase配置和客户端初始化

(function(window) {
    'use strict';

    // Supabase配置
    window.SUPABASE_URL = 'https://snckktsqwrbfwtjlvcfr.supabase.co';
    window.SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNuY2trdHNxd3JiZnd0amx2Y2ZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMDAwMDksImV4cCI6MjA2NjY3NjAwOX0.L83td9BHYz7Z6vQke7CXPZrcOXcQ8FKg9duBL-fm644';

    // 如果supabase-simple.js已经加载，使用它
    if (window.supabaseClient) {
        window.supabase = window.supabaseClient;
        console.log('✅ 使用简化Supabase客户端');
    } else if (window.supabase && window.supabase.createClient) {
        // 如果有官方Supabase库，创建客户端
        window.supabase = window.supabase.createClient(window.SUPABASE_URL, window.SUPABASE_ANON_KEY);
        console.log('✅ 使用官方Supabase客户端');
    } else {
        console.warn('⚠️ 未找到Supabase客户端，请确保已加载supabase-simple.js或官方Supabase库');
    }

    // 导出配置供其他脚本使用
    window.SupabaseConfig = {
        url: window.SUPABASE_URL,
        anonKey: window.SUPABASE_ANON_KEY,
        client: window.supabase
    };

})(window);
