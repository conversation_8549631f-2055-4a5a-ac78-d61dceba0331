<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>认证测试页面</title>
    
    <!-- Supabase CDN -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🧪 认证系统测试</h1>
    
    <div class="container">
        <h2>初始化状态</h2>
        <div id="init-status"></div>
    </div>
    
    <div class="container">
        <h2>用户状态</h2>
        <div id="user-status"></div>
        <button onclick="checkAuth()">检查认证</button>
        <button onclick="testLogin()">测试登录</button>
    </div>
    
    <div class="container">
        <h2>详细日志</h2>
        <div id="logs"></div>
        <button onclick="clearLogs()">清除日志</button>
    </div>

    <!-- 只加载Supabase认证系统 -->
    <script src="supabase-simple.js"></script>
    <script src="js/auth-system.js"></script>
    
    <script>
        let logs = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            logs.push(`[${timestamp}] ${message}`);
            console.log(message);
            updateLogs();
        }
        
        function updateLogs() {
            const logsDiv = document.getElementById('logs');
            logsDiv.innerHTML = `<pre>${logs.slice(-20).join('\n')}</pre>`;
        }
        
        function clearLogs() {
            logs = [];
            updateLogs();
        }
        
        function updateInitStatus() {
            const statusDiv = document.getElementById('init-status');
            let html = '';
            
            if (typeof window.supabase !== 'undefined') {
                html += '<div class="status success">✅ window.supabase 存在</div>';
            } else {
                html += '<div class="status error">❌ window.supabase 不存在</div>';
            }
            
            if (typeof window.supabaseClient !== 'undefined') {
                html += '<div class="status success">✅ window.supabaseClient 存在</div>';
            } else {
                html += '<div class="status error">❌ window.supabaseClient 不存在</div>';
            }
            
            if (window.supabaseInitialized) {
                html += '<div class="status success">✅ Supabase已初始化</div>';
            } else {
                html += '<div class="status warning">⚠️ Supabase未初始化</div>';
            }
            
            statusDiv.innerHTML = html;
        }
        
        async function checkAuth() {
            log('开始检查认证状态...');
            
            if (!window.supabaseClient) {
                log('错误: supabaseClient不存在', 'error');
                return;
            }
            
            try {
                const { data: { user }, error } = await window.supabaseClient.auth.getUser();
                
                if (error) {
                    log(`认证检查错误: ${error.message}`, 'error');
                    return;
                }
                
                if (user) {
                    log(`用户已登录: ${user.email}`, 'success');
                    
                    // 查询用户详细信息
                    const { data: userData, error: userError } = await window.supabaseClient
                        .from('users')
                        .select('*')
                        .eq('email', user.email)
                        .single();
                    
                    if (userError) {
                        log(`用户数据查询错误: ${userError.message}`, 'error');
                    } else {
                        log(`用户数据: ${JSON.stringify(userData)}`, 'info');
                        updateUserStatus(userData);
                    }
                } else {
                    log('用户未登录', 'warning');
                    updateUserStatus(null);
                }
            } catch (error) {
                log(`检查认证时出错: ${error.message}`, 'error');
            }
        }
        
        function updateUserStatus(userData) {
            const statusDiv = document.getElementById('user-status');
            
            if (userData) {
                statusDiv.innerHTML = `
                    <div class="status success">✅ 用户已登录</div>
                    <div class="status info">📧 邮箱: ${userData.email}</div>
                    <div class="status info">👤 用户名: ${userData.username}</div>
                    <div class="status info">🔑 权限: ${userData.user_type}</div>
                    <div class="status info">✅ 激活状态: ${userData.is_active ? '是' : '否'}</div>
                `;
            } else {
                statusDiv.innerHTML = '<div class="status warning">⚠️ 用户未登录</div>';
            }
        }
        
        async function testLogin() {
            const email = prompt('请输入邮箱:');
            const password = prompt('请输入密码:');
            
            if (!email || !password) return;
            
            log(`尝试登录: ${email}`);
            
            try {
                const { data, error } = await window.supabaseClient.auth.signInWithPassword({
                    email: email,
                    password: password
                });
                
                if (error) {
                    log(`登录失败: ${error.message}`, 'error');
                } else {
                    log('登录成功!', 'success');
                    setTimeout(checkAuth, 1000);
                }
            } catch (error) {
                log(`登录时出错: ${error.message}`, 'error');
            }
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成');
            updateInitStatus();
            
            // 监听Supabase初始化完成事件
            window.addEventListener('supabaseReady', function() {
                log('Supabase初始化完成');
                updateInitStatus();
                checkAuth();
            });
            
            // 如果已经初始化，立即检查
            if (window.supabaseInitialized) {
                setTimeout(() => {
                    updateInitStatus();
                    checkAuth();
                }, 500);
            }
        });
    </script>
</body>
</html>
