<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Supabase简单测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>Supabase简单测试</h1>
    
    <div id="status"></div>
    
    <button onclick="testSupabaseClient()">测试Supabase客户端</button>
    <button onclick="testDatabaseConnection()">测试数据库连接</button>
    <button onclick="createTestUser()">创建测试用户</button>
    
    <div id="results"></div>

    <script src="supabase-simple.js"></script>
    
    <script>
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function showResults(data) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
        }

        function testSupabaseClient() {
            showStatus('测试Supabase客户端...', 'info');
            
            console.log('window.supabase:', window.supabase);
            console.log('window.supabaseClient:', window.supabaseClient);
            
            const results = {
                hasSupabase: typeof window.supabase !== 'undefined',
                hasSupabaseClient: typeof window.supabaseClient !== 'undefined',
                hasCreateClient: typeof window.supabase?.createClient === 'function',
                hasFromMethod: typeof window.supabaseClient?.from === 'function',
                clientType: typeof window.supabaseClient
            };
            
            if (results.hasSupabaseClient && results.hasFromMethod) {
                showStatus('✅ Supabase客户端正常', 'success');
            } else {
                showStatus('❌ Supabase客户端有问题', 'error');
            }
            
            showResults(results);
        }

        async function testDatabaseConnection() {
            try {
                showStatus('测试数据库连接...', 'info');
                
                if (!window.supabaseClient) {
                    throw new Error('Supabase客户端未初始化');
                }
                
                if (typeof window.supabaseClient.from !== 'function') {
                    throw new Error('Supabase客户端缺少from方法');
                }
                
                // 尝试查询用户表
                const { data, error } = await window.supabaseClient
                    .from('users')
                    .select('count')
                    .limit(1);
                
                if (error) {
                    showStatus('❌ 数据库连接失败: ' + error.message, 'error');
                    showResults(error);
                } else {
                    showStatus('✅ 数据库连接成功', 'success');
                    showResults({ message: '数据库连接正常', data });
                }
                
            } catch (error) {
                showStatus('❌ 测试失败: ' + error.message, 'error');
                showResults(error);
                console.error('数据库连接测试失败:', error);
            }
        }

        async function createTestUser() {
            try {
                showStatus('创建测试用户...', 'info');
                
                if (!window.supabaseClient) {
                    throw new Error('Supabase客户端未初始化');
                }
                
                // 简单的密码哈希
                function hashPassword(password) {
                    return btoa(password + 'chunsheng_salt');
                }
                
                const userData = {
                    email: '<EMAIL>',
                    name: '测试用户',
                    password: hashPassword('123456'),
                    user_type: 'premium',
                    created_at: new Date().toISOString()
                };
                
                const { data, error } = await window.supabaseClient
                    .from('users')
                    .insert([userData])
                    .select()
                    .single();
                
                if (error) {
                    if (error.message.includes('duplicate') || error.code === '23505') {
                        showStatus('⚠️ 用户已存在', 'info');
                        showResults({ message: '用户已存在，可以尝试登录' });
                    } else {
                        showStatus('❌ 创建用户失败: ' + error.message, 'error');
                        showResults(error);
                    }
                } else {
                    showStatus('✅ 用户创建成功', 'success');
                    showResults(data);
                }
                
            } catch (error) {
                showStatus('❌ 创建用户时出错: ' + error.message, 'error');
                showResults(error);
                console.error('创建用户失败:', error);
            }
        }

        // 页面加载时自动测试
        window.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                testSupabaseClient();
            }, 500);
        });
    </script>
</body>
</html>
