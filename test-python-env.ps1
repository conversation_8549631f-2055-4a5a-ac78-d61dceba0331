# Test Python environment variables

Write-Host "Testing Python Environment Variables" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green

# Test 1: Check if Python paths are in environment variables
Write-Host "`n1. Checking PATH environment variable..." -ForegroundColor Yellow
$userPath = [Environment]::GetEnvironmentVariable("Path", [EnvironmentVariableTarget]::User)
$systemPath = [Environment]::GetEnvironmentVariable("Path", [EnvironmentVariableTarget]::Machine)
$currentPath = $env:Path

Write-Host "User PATH contains Python:" -ForegroundColor Cyan
$userPath -split ';' | Where-Object { $_ -like "*Python*" } | ForEach-Object {
    Write-Host "  $_" -ForegroundColor Green
}

Write-Host "`nCurrent session PATH contains Python:" -ForegroundColor Cyan
$currentPath -split ';' | Where-Object { $_ -like "*Python*" } | ForEach-Object {
    Write-Host "  $_" -ForegroundColor Green
}

# Test 2: Check if Python executable exists
Write-Host "`n2. Checking Python executable..." -ForegroundColor Yellow
$pythonExe = "D:\Users\Administrator\AppData\Local\Programs\Python\Python313\python.exe"
if (Test-Path $pythonExe) {
    Write-Host "Python executable found: $pythonExe" -ForegroundColor Green
    
    # Get Python version using full path
    try {
        $version = & $pythonExe --version 2>&1
        Write-Host "Python version: $version" -ForegroundColor Green
    } catch {
        Write-Host "Error getting Python version: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "Python executable NOT found: $pythonExe" -ForegroundColor Red
}

# Test 3: Test python command availability
Write-Host "`n3. Testing python command..." -ForegroundColor Yellow
try {
    $result = Get-Command python -ErrorAction SilentlyContinue
    if ($result) {
        Write-Host "python command found at: $($result.Source)" -ForegroundColor Green
        $version = & python --version 2>&1
        Write-Host "Version: $version" -ForegroundColor Green
    } else {
        Write-Host "python command NOT found in PATH" -ForegroundColor Red
    }
} catch {
    Write-Host "Error testing python command: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4: Test pip command availability
Write-Host "`n4. Testing pip command..." -ForegroundColor Yellow
try {
    $result = Get-Command pip -ErrorAction SilentlyContinue
    if ($result) {
        Write-Host "pip command found at: $($result.Source)" -ForegroundColor Green
        $version = & pip --version 2>&1
        Write-Host "Version: $version" -ForegroundColor Green
    } else {
        Write-Host "pip command NOT found in PATH" -ForegroundColor Red
    }
} catch {
    Write-Host "Error testing pip command: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 5: Manual PATH refresh
Write-Host "`n5. Refreshing environment variables..." -ForegroundColor Yellow
$env:Path = [Environment]::GetEnvironmentVariable("Path", [EnvironmentVariableTarget]::User) + ";" + [Environment]::GetEnvironmentVariable("Path", [EnvironmentVariableTarget]::Machine)
Write-Host "Environment variables refreshed" -ForegroundColor Green

# Test python command again after refresh
Write-Host "`n6. Testing python command after refresh..." -ForegroundColor Yellow
try {
    $result = Get-Command python -ErrorAction SilentlyContinue
    if ($result) {
        Write-Host "python command found at: $($result.Source)" -ForegroundColor Green
        $version = & python --version 2>&1
        Write-Host "Version: $version" -ForegroundColor Green
    } else {
        Write-Host "python command still NOT found in PATH" -ForegroundColor Red
        Write-Host "You may need to restart your terminal or computer" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Error testing python command: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n====================================" -ForegroundColor Green
Write-Host "Python environment test completed" -ForegroundColor Green

# Instructions
Write-Host "`nIf python command is still not working:" -ForegroundColor Yellow
Write-Host "1. Close this PowerShell window" -ForegroundColor White
Write-Host "2. Open a new PowerShell window" -ForegroundColor White
Write-Host "3. Test: python --version" -ForegroundColor White
Write-Host "4. If still not working, restart your computer" -ForegroundColor White
