# Add Python to system environment variables

Write-Host "Adding Python to system environment variables..." -ForegroundColor Green

# Python installation paths
$pythonPath = "D:\Users\Administrator\AppData\Local\Programs\Python\Python313\"
$pythonScriptsPath = "D:\Users\Administrator\AppData\Local\Programs\Python\Python313\Scripts\"

# Check if Python exists
if (Test-Path $pythonPath) {
    Write-Host "Found Python installation: $pythonPath" -ForegroundColor Green
} else {
    Write-Host "Python installation not found: $pythonPath" -ForegroundColor Red
    Write-Host "Please check if Python is correctly installed" -ForegroundColor Yellow
    exit 1
}

# Get current user environment variable
$currentUserPath = [Environment]::GetEnvironmentVariable("Path", [EnvironmentVariableTarget]::User)
Write-Host "Current user PATH: $currentUserPath" -ForegroundColor Cyan

# Check if Python paths already exist
$pathsToAdd = @()
if ($currentUserPath -notlike "*$pythonPath*") {
    $pathsToAdd += $pythonPath
    Write-Host "Need to add Python main directory" -ForegroundColor Yellow
} else {
    Write-Host "Python main directory already in PATH" -ForegroundColor Green
}

if ($currentUserPath -notlike "*$pythonScriptsPath*") {
    $pathsToAdd += $pythonScriptsPath
    Write-Host "Need to add Python Scripts directory" -ForegroundColor Yellow
} else {
    Write-Host "Python Scripts directory already in PATH" -ForegroundColor Green
}

# Add paths to environment variable
if ($pathsToAdd.Count -gt 0) {
    $newPath = $currentUserPath
    foreach ($path in $pathsToAdd) {
        if ($newPath -ne "") {
            $newPath += ";"
        }
        $newPath += $path
    }

    try {
        [Environment]::SetEnvironmentVariable("Path", $newPath, [EnvironmentVariableTarget]::User)
        Write-Host "Successfully added Python paths to user environment variable" -ForegroundColor Green

        # Update current session environment variable
        $env:Path = $newPath
        Write-Host "Updated current session environment variable" -ForegroundColor Green

    } catch {
        Write-Host "Failed to add environment variable: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "Python paths already exist, no need to add" -ForegroundColor Blue
}

# Test Python command
Write-Host "`nTesting Python command..." -ForegroundColor Green
try {
    $pythonVersion = & python --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Python command test successful: $pythonVersion" -ForegroundColor Green
    } else {
        Write-Host "Python command test failed" -ForegroundColor Red
        Write-Host "Please restart PowerShell or Command Prompt" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Python command not available, may need to restart terminal" -ForegroundColor Red
    Write-Host "Please close current terminal and reopen" -ForegroundColor Yellow
}

# Test pip command
Write-Host "`nTesting pip command..." -ForegroundColor Green
try {
    $pipVersion = & pip --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "pip command test successful: $pipVersion" -ForegroundColor Green
    } else {
        Write-Host "pip command test failed" -ForegroundColor Red
    }
} catch {
    Write-Host "pip command not available" -ForegroundColor Red
}

Write-Host "`nPython environment variable configuration completed!" -ForegroundColor Green
Write-Host "If commands are still not available, please:" -ForegroundColor Yellow
Write-Host "1. Restart PowerShell or Command Prompt" -ForegroundColor Yellow
Write-Host "2. Or restart the computer" -ForegroundColor Yellow
Write-Host "3. Then test: python --version" -ForegroundColor Yellow

# Show final PATH
Write-Host "`nFinal user PATH environment variable:" -ForegroundColor Cyan
$finalPath = [Environment]::GetEnvironmentVariable("Path", [EnvironmentVariableTarget]::User)
$finalPath -split ';' | ForEach-Object {
    if ($_ -like "*Python*") {
        Write-Host "  Python: $_" -ForegroundColor Green
    } else {
        Write-Host "         $_" -ForegroundColor Gray
    }
}
