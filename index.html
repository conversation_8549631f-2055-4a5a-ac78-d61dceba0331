<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>安徽春晟机械有限公司_减震器冲压件_减震器冲压件厂家</title>
    <meta name="description" content="安徽春晟机械有限公司是专业从事减震器冲压件设计与生产的企业。公司坐落于安徽省广德经济开发区，地处苏浙皖三省交界处，交通便利，环境优雅。">
    <meta name="keywords" content="安徽春晟机械有限公司,减震器冲压件,减震器冲压件厂家">
    
    <!-- 原网站样式 -->
    <link href="pcstyle.css" rel="stylesheet" type="text/css">
    <link href="reset.css" rel="stylesheet" type="text/css">
    <link href="32_Pc_zh-CN.css" rel="stylesheet" type="text/css">
    
    <!-- 自定义样式 -->
    <link href="css/custom.css" rel="stylesheet" type="text/css">
    <link href="css/customer-service-chat.css" rel="stylesheet" type="text/css">

    <!-- Supabase -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>

    <!-- jQuery -->
    <script src="jquery-3.6.3.min.js"></script>
</head>
<body>
    <!-- 头部区域 -->
    <header class="header-section">
        <!-- Logo区域 -->
        <div class="logo-section" style="background-color: rgb(37, 37, 37); padding: 10px 0;">
            <div class="container" style="width: 1200px; margin: 0 auto; display: flex; justify-content: space-between; align-items: center;">
                <div class="logo">
                    <img src="16792504.png" alt="安徽春晟机械有限公司" style="height: 60px;">
                </div>
                <div class="user-actions">
                    <a href="login.html" class="login-btn" style="color: white; margin-right: 15px;">登录</a>
                    <a href="register.html" class="register-btn" style="background: #be131b; color: white; padding: 5px 15px;">注册</a>
                </div>
            </div>
        </div>
        
        <!-- 导航栏 -->
        <nav class="navigation" style="background-color: #F5F5F5; border-bottom: 1px solid #ddd;">
            <div class="container" style="width: 1200px; margin: 0 auto;">
                <ul class="nav-menu" style="display: flex; list-style: none; margin: 0; padding: 0;">
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="index.html" style="display: block; padding: 20px; color: #be131b; text-decoration: none; transition: all 0.3s; font-weight: bold;">网站首页</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="about.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">关于我们</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="factory.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">企业展示</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="factory.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">工厂设备</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="products.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">产品中心</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="news.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">新闻中心</a>
                    </li>
                    <li style="flex: 1; text-align: center;">
                        <a href="contact.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">联系我们</a>
                    </li>
                </ul>
            </div>
        </nav>
    </header>

    <!-- 轮播图区域 -->
    <section class="hero-section">
        <div class="slider-container" style="position: relative; height: 600px; overflow: hidden;">
            <div class="slide active" style="position: absolute; width: 100%; height: 100%; background: url('16636630.jpg') center/cover;">
                <div class="slide-content" style="position: absolute; left: 100px; top: 50%; transform: translateY(-50%); color: white; z-index: 2;">
                    <h1 style="font-size: 48px; margin-bottom: 20px;">专业生产减震器冲压件</h1>
                    <p style="font-size: 18px; margin-bottom: 30px;">安徽春晟机械有限公司 - 专业从事减震器冲压件设计与生产</p>
                    <a href="products.html" class="cta-button" style="background: #be131b; color: white; padding: 12px 30px; text-decoration: none; border-radius: 3px;">了解更多</a>
                </div>
            </div>
            <div class="slide" style="position: absolute; width: 100%; height: 100%; background: url('16719895.jpg') center/cover;">
                <div class="slide-content" style="position: absolute; left: 100px; top: 50%; transform: translateY(-50%); color: white; z-index: 2;">
                    <h1 style="font-size: 48px; margin-bottom: 20px;">高品质汽车零部件制造</h1>
                    <p style="font-size: 18px; margin-bottom: 30px;">采用先进的冲压工艺，确保产品质量稳定可靠</p>
                    <a href="products.html" class="cta-button" style="background: #be131b; color: white; padding: 12px 30px; text-decoration: none; border-radius: 3px;">产品展示</a>
                </div>
            </div>


            <!-- 轮播控制按钮 -->
            <button class="slider-prev" onclick="changeSlide(-1)" style="position: absolute; left: 20px; top: 50%; transform: translateY(-50%); background: rgba(255,255,255,0.3); border: none; color: white; padding: 15px; border-radius: 50%; cursor: pointer; z-index: 3;">‹</button>
            <button class="slider-next" onclick="changeSlide(1)" style="position: absolute; right: 20px; top: 50%; transform: translateY(-50%); background: rgba(255,255,255,0.3); border: none; color: white; padding: 15px; border-radius: 50%; cursor: pointer; z-index: 3;">›</button>

            <!-- 轮播指示器 -->
            <div class="slider-indicators" style="position: absolute; bottom: 20px; left: 50%; transform: translateX(-50%); display: flex; gap: 10px; z-index: 3;">
                <span class="indicator active" onclick="currentSlide(1)" style="width: 12px; height: 12px; border-radius: 50%; background: white; cursor: pointer; opacity: 1;"></span>
                <span class="indicator" onclick="currentSlide(2)" style="width: 12px; height: 12px; border-radius: 50%; background: white; cursor: pointer; opacity: 0.5;"></span>
            </div>
        </div>
    </section>

    <!-- 公司介绍区域 -->
    <section class="company-intro" style="padding: 80px 0; background: white;">
        <div class="container" style="width: 1200px; margin: 0 auto; display: flex; align-items: center;">
            <div class="intro-content" style="flex: 1; padding-right: 50px;">
                <h2 style="font-size: 32px; color: #222; margin-bottom: 20px;">安徽春晟机械有限公司</h2>
                <p style="font-size: 16px; line-height: 1.8; color: #444; margin-bottom: 20px;">
                    本公司系专业从事减震器冲压件设计与生产的企业。公司坐落于安徽省广德经济开发区，地处苏浙皖三省交界处，交通便利，环境优雅。
                    距离上海230公里，杭州100公里，宁波280公里，南京230公里，拥有天然的地理优势。
                </p>
                <p style="font-size: 16px; line-height: 1.8; color: #444; margin-bottom: 30px;">
                    工厂占地40亩（建筑面积22000㎡），包括综合办公楼，生产车间，模具仓库，材料仓库，物流区域，员工宿舍，员工食堂，以及员工文娱场所等。
                </p>
                <div class="action-buttons">
                    <a href="about.html" style="background: #be131b; color: white; padding: 10px 20px; text-decoration: none; margin-right: 15px;">企业文化</a>
                    <a href="about.html" style="background: #be131b; color: white; padding: 10px 20px; text-decoration: none; margin-right: 15px;">发展历程</a>
                    <a href="about.html" style="background: #be131b; color: white; padding: 10px 20px; text-decoration: none;">组织架构</a>
                </div>
            </div>
            <div class="intro-image" style="flex: 1;">
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                    <img src="16028205.jpg" alt="公司厂房" style="width: 100%; height: 200px; object-fit: cover; border-radius: 5px;">
                    <img src="16719909.jpg" alt="生产车间" style="width: 100%; height: 200px; object-fit: cover; border-radius: 5px;">
                    <img src="16719937.jpg" alt="产品展示" style="width: 100%; height: 200px; object-fit: cover; border-radius: 5px;">
                    <img src="16719962.jpg" alt="设备展示" style="width: 100%; height: 200px; object-fit: cover; border-radius: 5px;">
                </div>
            </div>
        </div>
    </section>

    <!-- 产品中心区域 -->
    <section class="product-center-section" style="padding: 60px 0; background: #f8f9fa;">
        <div class="container" style="width: 1200px; margin: 0 auto;">
            <!-- 产品中心标题 -->
            <div class="section-header" style="text-align: center; margin-bottom: 50px;">
                <h2 style="font-size: 32px; color: #222; margin-bottom: 15px;">产品中心</h2>
                <p style="font-size: 16px; color: #666;">专业生产各类减震器冲压件产品</p>
            </div>
            <div class="product-center-layout" style="display: flex; gap: 30px; min-height: 600px;">

                <!-- 左侧分类菜单 -->
                <div class="category-sidebar" style="width: 300px; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); overflow: hidden;">
                    <!-- 分类标题 -->
                    <div class="category-header" style="background: #be131b; color: white; padding: 20px; text-align: center;">
                        <h2 style="margin: 0; font-size: 24px; font-weight: bold;">产品分类</h2>
                    </div>

                    <!-- 分类列表 -->
                    <div class="category-list">
                        <div class="category-item active" data-category="支架（座）类" style="padding: 15px 20px; border-bottom: 1px dotted #ddd; cursor: pointer; transition: all 0.3s; background: #f8f9fa; border-left: 4px solid #be131b;">
                            <span style="color: #be131b; font-weight: bold;">支架（座）类</span>
                            <i style="float: right; color: #be131b;">▶</i>
                        </div>

                        <div class="category-item" data-category="固定圈（防护套）类" style="padding: 15px 20px; border-bottom: 1px dotted #ddd; cursor: pointer; transition: all 0.3s;">
                            <span style="color: #666;">固定圈（防护套）类</span>
                            <i style="float: right; color: #ccc;">▶</i>
                        </div>

                        <div class="category-item" data-category="支耳（板）类" style="padding: 15px 20px; border-bottom: 1px dotted #ddd; cursor: pointer; transition: all 0.3s;">
                            <span style="color: #666;">支耳（板）类</span>
                            <i style="float: right; color: #ccc;">▶</i>
                        </div>

                        <div class="category-item" data-category="弹簧盘类" style="padding: 15px 20px; border-bottom: 1px dotted #ddd; cursor: pointer; transition: all 0.3s;">
                            <span style="color: #666;">弹簧盘类</span>
                            <i style="float: right; color: #ccc;">▶</i>
                        </div>

                        <div class="category-item" data-category="防尘盖（顶板）类" style="padding: 15px 20px; border-bottom: 1px dotted #ddd; cursor: pointer; transition: all 0.3s;">
                            <span style="color: #666;">防尘盖（顶板）类</span>
                            <i style="float: right; color: #ccc;">▶</i>
                        </div>

                        <div class="category-item" data-category="其它类" style="padding: 15px 20px; cursor: pointer; transition: all 0.3s;">
                            <span style="color: #666;">其它类</span>
                            <i style="float: right; color: #ccc;">▶</i>
                        </div>
                    </div>
                </div>

                <!-- 右侧产品展示区域 -->
                <div class="product-display" style="flex: 1; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); padding: 30px;">
                    <div id="featured-products" class="featured-products" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                        <!-- 产品将通过JavaScript动态加载 -->
                    </div>
                    <div class="text-center" style="margin-top: 30px; text-align: center;">
                        <a href="products.html" style="background: #be131b; color: white; padding: 12px 30px; text-decoration: none; border-radius: 3px;">查看更多产品</a>
                    </div>
                </div>
            </div>
        </div>
    </section>



    <!-- 页脚 -->
    <footer style="background: #333; color: white; padding: 40px 0;">
        <div class="container" style="width: 1200px; margin: 0 auto; text-align: center;">
            <p>&copy; 2024 安徽春晟机械有限公司. 保留所有权利.</p>
            <p>地址：安徽省广德经济开发区 | 专业从事减震器冲压件设计与生产</p>
        </div>
    </footer>

    <!-- 产品中心样式 -->
    <style>
        .category-item {
            transition: all 0.3s ease;
        }

        .category-item:hover {
            background: #f8f9fa !important;
            border-left: 4px solid #be131b !important;
        }

        .category-item:hover span {
            color: #be131b !important;
            font-weight: bold !important;
        }

        .category-item:hover i {
            color: #be131b !important;
        }

        .category-item.active {
            background: #f8f9fa !important;
            border-left: 4px solid #be131b !important;
        }

        .category-item.active span {
            color: #be131b !important;
            font-weight: bold !important;
        }

        .category-item.active i {
            color: #be131b !important;
        }

        .product-item {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .product-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .product-item img {
            width: 100%;
            height: 150px;
            object-fit: cover;
        }

        .product-item .product-info {
            padding: 15px;
        }

        .product-item .product-name {
            font-size: 14px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
            text-align: center;
        }

        .product-item .product-code {
            font-size: 12px;
            color: #666;
            text-align: center;
        }

        @media (max-width: 768px) {
            .product-center-layout {
                flex-direction: column !important;
            }

            .category-sidebar {
                width: 100% !important;
                margin-bottom: 20px;
            }

            .product-display {
                padding: 20px !important;
            }

            .container {
                width: 95% !important;
                padding: 0 10px !important;
            }
        }
    </style>

    <!-- JavaScript -->
    <script src="supabase-simple.js"></script>
    <script src="js/auth-system.js"></script>
    <script src="js/main.js"></script>
    <script>
        // 轮播图功能
        let currentSlideIndex = 0;
        const slides = document.querySelectorAll('.slide');
        const indicators = document.querySelectorAll('.indicator');

        function showSlide(index) {
            slides.forEach((slide, i) => {
                slide.classList.toggle('active', i === index);
            });
            indicators.forEach((indicator, i) => {
                indicator.style.opacity = i === index ? '1' : '0.5';
            });
        }

        function changeSlide(direction) {
            currentSlideIndex += direction;
            if (currentSlideIndex >= slides.length) currentSlideIndex = 0;
            if (currentSlideIndex < 0) currentSlideIndex = slides.length - 1;
            showSlide(currentSlideIndex);
        }

        function currentSlide(index) {
            currentSlideIndex = index - 1;
            showSlide(currentSlideIndex);
        }

        // 自动轮播
        setInterval(() => {
            changeSlide(1);
        }, 5000);

        // 产品中心功能
        let allProducts = [];
        let currentCategory = '支架（座）类';

        // 产品分类映射（数据库代码 -> 中文名称）
        const categoryMapping = {
            'ZJ': '支架（座）类',
            'GD': '固定圈（防护套）类',
            'ZE': '支耳（板）类',
            'TP': '弹簧盘类',
            'FC': '防尘盖（顶板）类',
            'QT': '其它类',
            // 兼容旧数据
            'DB': '防尘盖（顶板）类',
            'ZC': '支架（座）类'
        };

        // 中文分类 -> 数据库代码的反向映射
        const reverseCategoryMapping = {
            '支架（座）类': ['ZJ', 'ZC'],
            '固定圈（防护套）类': ['GD'],
            '支耳（板）类': ['ZE'],
            '弹簧盘类': ['TP'],
            '防尘盖（顶板）类': ['FC', 'DB'],
            '其它类': ['QT']
        };

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeProductCenter();
        });

        // 初始化产品中心
        async function initializeProductCenter() {
            try {
                // 加载产品数据
                await loadProducts();

                // 初始化分类切换功能
                initializeCategorySwitch();

                // 显示默认分类的产品
                displayProductsByCategory(currentCategory);

            } catch (error) {
                console.error('初始化产品中心失败:', error);
            }
        }

        // 加载产品数据
        async function loadProducts() {
            try {
                const { data: products, error } = await supabase
                    .from('products')
                    .select('*')
                    .order('created_at', { ascending: false });

                if (error) {
                    console.error('加载产品失败:', error);
                    return;
                }

                allProducts = products || [];
                console.log('加载了', allProducts.length, '个产品');

            } catch (error) {
                console.error('加载产品数据失败:', error);
            }
        }

        // 初始化分类切换功能
        function initializeCategorySwitch() {
            const categoryItems = document.querySelectorAll('.category-item');

            categoryItems.forEach(item => {
                item.addEventListener('click', function() {
                    // 移除所有active类
                    categoryItems.forEach(i => i.classList.remove('active'));

                    // 添加active类到当前项
                    this.classList.add('active');

                    // 获取选中的分类
                    const selectedCategory = this.getAttribute('data-category');
                    currentCategory = selectedCategory;

                    // 显示对应分类的产品
                    displayProductsByCategory(selectedCategory);
                });
            });
        }

        // 根据分类显示产品
        function displayProductsByCategory(category) {
            const container = document.getElementById('featured-products');

            // 获取该分类对应的数据库代码
            const categoryCodes = reverseCategoryMapping[category] || [];

            // 筛选产品
            const filteredProducts = allProducts.filter(product => {
                // 检查产品的分类代码是否匹配
                return categoryCodes.includes(product.product_category);
            });

            console.log(`筛选分类: ${category}, 代码: ${categoryCodes}, 找到产品: ${filteredProducts.length}`);

            if (filteredProducts.length === 0) {
                container.innerHTML = `
                    <div style="grid-column: 1 / -1; text-align: center; padding: 40px; color: #666;">
                        <p style="font-size: 16px;">暂无${category}产品</p>
                        <a href="products.html" style="color: #be131b; text-decoration: none;">查看所有产品 →</a>
                    </div>
                `;
                return;
            }

            // 显示产品（最多显示8个）
            const productsToShow = filteredProducts.slice(0, 8);

            container.innerHTML = productsToShow.map(product => `
                <div class="product-item" onclick="viewProduct('${product.id}')">
                    <img src="${product.product_image || 'placeholder.svg'}" alt="${product.product_name}" onerror="this.src='placeholder.svg'">
                    <div class="product-info">
                        <div class="product-name">${product.product_name}</div>
                        <div class="product-code">${product.stock_code || ''}</div>
                    </div>
                </div>
            `).join('');
        }

        // 查看产品详情
        function viewProduct(productId) {
            window.location.href = `product-detail.html?id=${productId}`;
        }
    </script>

    <!-- 客服系统脚本 -->
    <script src="js/customer-service-chat.js"></script>
</body>
</html>
