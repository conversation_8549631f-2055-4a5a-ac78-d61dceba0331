// 客服聊天组件

class CustomerServiceChat {
    constructor() {
        this.isOpen = false;
        this.messagesSubscription = null;
        this.isConnected = false;
        this.guestId = this.getOrCreateGuestId();
        this.init();
    }

    // 获取或创建游客ID
    getOrCreateGuestId() {
        let guestId = this.getCookie('guest_id');
        if (!guestId) {
            guestId = 'guest_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            this.setCookie('guest_id', guestId, 30); // 30天过期
        }
        return guestId;
    }

    // 设置Cookie
    setCookie(name, value, days) {
        const expires = new Date();
        expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
        document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/`;
    }

    // 获取<PERSON>ie
    getCookie(name) {
        const nameEQ = name + "=";
        const ca = document.cookie.split(';');
        for (let i = 0; i < ca.length; i++) {
            let c = ca[i];
            while (c.charAt(0) === ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
        }
        return null;
    }

    // 初始化聊天组件
    init() {
        this.createChatWidget();
        this.bindEvents();
        this.checkConnection();
    }

    // 创建聊天窗口HTML
    createChatWidget() {
        const chatWidget = document.createElement('div');
        chatWidget.className = 'cs-chat-widget';
        chatWidget.innerHTML = `
            <button class="cs-chat-button" id="cs-chat-toggle">
                💬
            </button>
            <div class="cs-chat-window" id="cs-chat-window">
                <div class="cs-chat-header">
                    <div>
                        <h4>在线客服</h4>
                        <div class="cs-chat-status">
                            <div class="cs-status-dot"></div>
                            <span>客服在线</span>
                        </div>
                    </div>
                    <button class="cs-chat-close" id="cs-chat-close">×</button>
                </div>
                <div class="cs-chat-messages" id="cs-chat-messages">
                    <div class="cs-welcome-message">
                        <h5>欢迎咨询！</h5>
                        <p>我们的客服团队随时为您提供帮助。请描述您的问题，我们会尽快回复。</p>
                        <div class="cs-quick-actions">
                            <div class="cs-quick-action" onclick="customerServiceChat.sendQuickMessage('产品咨询')">产品咨询</div>
                            <div class="cs-quick-action" onclick="customerServiceChat.sendQuickMessage('技术支持')">技术支持</div>
                            <div class="cs-quick-action" onclick="customerServiceChat.sendQuickMessage('售后服务')">售后服务</div>
                        </div>
                    </div>
                </div>
                <div class="cs-connection-status" id="cs-connection-status">
                    正在连接...
                </div>
                <div class="cs-chat-input">
                    <input type="text" id="cs-message-input" placeholder="输入您的消息..." maxlength="500">
                    <button class="cs-chat-send" id="cs-chat-send">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                        </svg>
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(chatWidget);
    }

    // 绑定事件
    bindEvents() {
        const toggleBtn = document.getElementById('cs-chat-toggle');
        const closeBtn = document.getElementById('cs-chat-close');
        const sendBtn = document.getElementById('cs-chat-send');
        const messageInput = document.getElementById('cs-message-input');

        toggleBtn.addEventListener('click', () => this.toggleChat());
        closeBtn.addEventListener('click', () => this.closeChat());
        sendBtn.addEventListener('click', () => this.sendMessage());
        
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendMessage();
            }
        });

        // 点击外部关闭聊天窗口
        document.addEventListener('click', (e) => {
            const chatWidget = document.querySelector('.cs-chat-widget');
            if (this.isOpen && !chatWidget.contains(e.target)) {
                this.closeChat();
            }
        });
    }

    // 检查连接状态
    async checkConnection() {
        try {
            // 检查Supabase连接 - 使用正确的客户端
            const supabaseClient = window.supabaseClient || window.supabase?.createClient?.(
                'https://snckktsqwrbfwtjlvcfr.supabase.co',
                'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNuY2trdHNxd3JiZnd0amx2Y2ZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMDAwMDksImV4cCI6MjA2NjY3NjAwOX0.L83td9BHYz7Z6vQke7CXPZrcOXcQ8FKg9duBL-fm644'
            );

            if (!supabaseClient) {
                throw new Error('Supabase客户端未初始化');
            }

            const { data, error } = await supabaseClient.from('customer_service_messages').select('id').limit(1);

            if (error) {
                this.updateConnectionStatus('连接失败', false);
                console.error('Supabase连接错误:', error);
                return;
            }

            // 加载历史消息（无论是否登录）
            await this.loadMessages();

            // 如果用户已登录，设置实时订阅
            if (currentUser) {
                this.setupRealtimeSubscription();
            }

            this.isConnected = true;
            this.updateConnectionStatus('已连接', true);

        } catch (error) {
            console.error('连接检查失败:', error);
            this.updateConnectionStatus('连接失败', false);
        }
    }

    // 更新连接状态
    updateConnectionStatus(message, isConnected) {
        const statusElement = document.getElementById('cs-connection-status');
        statusElement.textContent = message;
        statusElement.className = `cs-connection-status ${isConnected ? 'connected' : ''}`;
        this.isConnected = isConnected;
    }

    // 切换聊天窗口
    toggleChat() {
        if (this.isOpen) {
            this.closeChat();
        } else {
            this.openChat();
        }
    }

    // 打开聊天窗口
    async openChat() {
        this.isOpen = true;
        const chatWindow = document.getElementById('cs-chat-window');
        chatWindow.classList.add('show');

        // 检查连接状态
        if (!this.isConnected) {
            await this.checkConnection();
        } else {
            // 如果已连接，直接加载消息
            await this.loadMessages();
        }

        // 聚焦输入框
        setTimeout(() => {
            document.getElementById('cs-message-input').focus();
        }, 300);
    }

    // 关闭聊天窗口
    closeChat() {
        this.isOpen = false;
        const chatWindow = document.getElementById('cs-chat-window');
        chatWindow.classList.remove('show');
    }

    // 发送消息
    async sendMessage() {
        const messageInput = document.getElementById('cs-message-input');
        const messageContent = messageInput.value.trim();

        if (!messageContent) return;

        // 清空输入框
        messageInput.value = '';

        // 立即显示用户消息
        this.addMessage('user', messageContent);

        // 保存消息到本地存储
        this.saveMessageToLocal('user', messageContent);

        try {
            let guestInfo = null;

            // 如果是游客，获取游客信息
            if (!currentUser) {
                guestInfo = this.getGuestInfo();
                guestInfo.guest_id = this.guestId; // 添加游客ID
            }

            // 发送到服务器
            await sendCustomerServiceMessage(messageContent, guestInfo);

            // 显示提示
            setTimeout(() => {
                const autoReply = '您的消息已收到，客服人员会尽快回复，请稍候...';
                this.addMessage('admin', autoReply);
                this.saveMessageToLocal('admin', autoReply);
            }, 1000);

        } catch (error) {
            console.error('发送消息失败:', error);
            const errorMsg = '消息发送失败，请重试。';
            this.addMessage('admin', errorMsg);
            this.saveMessageToLocal('admin', errorMsg);
        }
    }

    // 发送快捷消息
    sendQuickMessage(message) {
        const messageInput = document.getElementById('cs-message-input');
        messageInput.value = message;
        this.sendMessage();
    }

    // 保存消息到本地存储
    saveMessageToLocal(senderType, content) {
        const storageKey = currentUser ? `chat_messages_${currentUser.id}` : `chat_messages_${this.guestId}`;
        let messages = JSON.parse(localStorage.getItem(storageKey) || '[]');

        const message = {
            id: Date.now(),
            sender_type: senderType,
            content: content,
            timestamp: new Date().toISOString()
        };

        messages.push(message);

        // 只保留最近100条消息
        if (messages.length > 100) {
            messages = messages.slice(-100);
        }

        localStorage.setItem(storageKey, JSON.stringify(messages));
    }

    // 从本地存储加载消息
    loadMessagesFromLocal() {
        const storageKey = currentUser ? `chat_messages_${currentUser.id}` : `chat_messages_${this.guestId}`;
        const messages = JSON.parse(localStorage.getItem(storageKey) || '[]');
        return messages;
    }

    // 获取游客信息
    getGuestInfo() {
        // 从localStorage获取游客信息，如果没有则返回默认值
        const guestInfo = localStorage.getItem('guest_info');
        if (guestInfo) {
            return JSON.parse(guestInfo);
        }
        return {
            name: '游客',
            email: null
        };
    }

    // 显示游客信息提示
    showGuestInfo() {
        const messagesContainer = document.getElementById('cs-chat-messages');
        const guestInfoHtml = `
            <div class="cs-guest-info">
                <div class="cs-message admin">
                    <div class="cs-message-content">
                        <p><strong>欢迎使用客服系统！</strong></p>
                        <p>您当前以游客身份使用客服功能。如需更好的服务体验，建议您先 <a href="login.html" target="_blank">登录</a> 或 <a href="register.html" target="_blank">注册</a>。</p>
                        <p>您也可以直接发送消息，我们的客服人员会尽快回复。</p>
                    </div>
                    <div class="cs-message-time">${new Date().toLocaleTimeString()}</div>
                </div>
            </div>
        `;
        messagesContainer.innerHTML = guestInfoHtml;
    }

    // 添加消息到界面
    addMessage(senderType, content, timestamp = null) {
        const messagesContainer = document.getElementById('cs-chat-messages');
        
        // 移除欢迎消息
        const welcomeMessage = messagesContainer.querySelector('.cs-welcome-message');
        if (welcomeMessage) {
            welcomeMessage.remove();
        }

        const messageElement = document.createElement('div');
        messageElement.className = `cs-message ${senderType}`;
        
        const time = timestamp ? new Date(timestamp).toLocaleTimeString() : new Date().toLocaleTimeString();
        
        messageElement.innerHTML = `
            <div class="cs-message-content">
                ${content}
                <div class="cs-message-time">${time}</div>
            </div>
        `;

        messagesContainer.appendChild(messageElement);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    // 添加系统消息
    addSystemMessage(content) {
        this.addMessage('system', content);
    }

    // 加载历史消息
    async loadMessages() {
        const messagesContainer = document.getElementById('cs-chat-messages');

        try {
            // 优先从本地存储加载消息
            const localMessages = this.loadMessagesFromLocal();

            // 清空现有消息
            messagesContainer.innerHTML = '';

            if (localMessages.length === 0) {
                // 如果是已登录用户，尝试从服务器加载
                if (currentUser) {
                    const serverMessages = await getUserCustomerServiceMessages();
                    if (serverMessages.length > 0) {
                        // 显示服务器消息并保存到本地
                        serverMessages.forEach(message => {
                            this.addMessage(message.message_type, message.message_content, message.created_at);
                            this.saveMessageToLocal(message.message_type, message.message_content);
                        });

                        // 标记管理员消息为已读
                        await markUserMessagesAsRead(currentUser.id);
                        return;
                    }
                }

                // 显示欢迎消息
                this.showWelcomeMessage();
            } else {
                // 显示本地历史消息
                localMessages.forEach(message => {
                    this.addMessage(message.sender_type, message.content, message.timestamp);
                });
            }

        } catch (error) {
            console.error('加载消息失败:', error);
            this.showWelcomeMessage();
        }
    }

    // 显示欢迎消息
    showWelcomeMessage() {
        const messagesContainer = document.getElementById('cs-chat-messages');
        messagesContainer.innerHTML = `
            <div class="cs-welcome-message">
                <h5>欢迎咨询！</h5>
                <p>我们的客服团队随时为您提供帮助。请描述您的问题，我们会尽快回复。</p>
                <div class="cs-quick-actions">
                    <div class="cs-quick-action" onclick="customerServiceChat.sendQuickMessage('产品咨询')">产品咨询</div>
                    <div class="cs-quick-action" onclick="customerServiceChat.sendQuickMessage('技术支持')">技术支持</div>
                    <div class="cs-quick-action" onclick="customerServiceChat.sendQuickMessage('售后服务')">售后服务</div>
                </div>
            </div>
        `;
    }

    // 设置实时订阅
    setupRealtimeSubscription() {
        if (!currentUser || this.messagesSubscription) return;

        const supabaseClient = window.supabaseClient || window.supabase?.createClient?.(
            'https://snckktsqwrbfwtjlvcfr.supabase.co',
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNuY2trdHNxd3JiZnd0amx2Y2ZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMDAwMDksImV4cCI6MjA2NjY3NjAwOX0.L83td9BHYz7Z6vQke7CXPZrcOXcQ8FKg9duBL-fm644'
        );

        if (!supabaseClient) {
            console.error('Supabase客户端未初始化，无法设置实时订阅');
            return;
        }

        this.messagesSubscription = supabaseClient
            .channel(`user_messages_${currentUser.id}`)
            .on('postgres_changes', {
                event: 'INSERT',
                schema: 'public',
                table: 'customer_service_messages',
                filter: `user_id=eq.${currentUser.id}`
            }, (payload) => {
                const message = payload.new;

                // 只显示管理员回复的消息
                if (message.message_type === 'admin') {
                    this.addMessage('admin', message.message_content, message.created_at);
                    this.saveMessageToLocal('admin', message.message_content);

                    // 如果聊天窗口未打开，显示新消息提示
                    if (!this.isOpen) {
                        this.showNewMessageNotification();
                    }
                }
            })
            .subscribe();
    }

    // 显示新消息通知
    showNewMessageNotification() {
        const toggleBtn = document.getElementById('cs-chat-toggle');
        toggleBtn.style.animation = 'pulse 1s infinite';
        
        setTimeout(() => {
            toggleBtn.style.animation = '';
        }, 3000);
    }

    // 清理资源
    destroy() {
        if (this.messagesSubscription) {
            const supabaseClient = window.supabaseClient || window.supabase?.createClient?.(
                'https://snckktsqwrbfwtjlvcfr.supabase.co',
                'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNuY2trdHNxd3JiZnd0amx2Y2ZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMDAwMDksImV4cCI6MjA2NjY3NjAwOX0.L83td9BHYz7Z6vQke7CXPZrcOXcQ8FKg9duBL-fm644'
            );

            if (supabaseClient && supabaseClient.removeChannel) {
                supabaseClient.removeChannel(this.messagesSubscription);
            }
        }
    }
}

// 全局实例
let customerServiceChat;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 延迟初始化，确保其他脚本已加载
    setTimeout(() => {
        customerServiceChat = new CustomerServiceChat();
    }, 1000);
});

// 页面卸载时清理
window.addEventListener('beforeunload', function() {
    if (customerServiceChat) {
        customerServiceChat.destroy();
    }
});

// 客服API函数
// 发送客服消息
async function sendCustomerServiceMessage(messageContent, guestInfo = null) {
    try {
        const supabaseClient = window.supabaseClient || window.supabase?.createClient?.(
            'https://snckktsqwrbfwtjlvcfr.supabase.co',
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNuY2trdHNxd3JiZnd0amx2Y2ZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMDAwMDksImV4cCI6MjA2NjY3NjAwOX0.L83td9BHYz7Z6vQke7CXPZrcOXcQ8FKg9duBL-fm644'
        );

        if (!supabaseClient) {
            throw new Error('Supabase客户端未初始化');
        }

        let messageData;

        if (currentUser) {
            // 已登录用户
            messageData = {
                user_id: currentUser.id,
                user_name: currentUser.name || currentUser.username || currentUser.email,
                user_email: currentUser.email,
                message_content: messageContent,
                message_type: 'user',
                is_read: false
            };
        } else {
            // 游客用户
            messageData = {
                user_id: null, // 游客没有user_id
                user_name: guestInfo?.name || '游客',
                user_email: guestInfo?.email || null,
                message_content: messageContent,
                message_type: 'user',
                is_read: false,
                guest_id: guestInfo?.guest_id || null
            };
        }

        const { data, error } = await supabaseClient
            .from('customer_service_messages')
            .insert([messageData])
            .select()
            .single();

        if (error) {
            console.error('发送消息失败:', error);
            throw error;
        }

        console.log('消息发送成功:', data);
        return data;

    } catch (error) {
        console.error('发送客服消息失败:', error);
        throw error;
    }
}

// 获取用户的客服消息
async function getUserCustomerServiceMessages() {
    try {
        if (!currentUser) {
            console.log('用户未登录，无法获取服务器消息');
            return [];
        }

        const supabaseClient = window.supabaseClient || window.supabase?.createClient?.(
            'https://snckktsqwrbfwtjlvcfr.supabase.co',
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNuY2trdHNxd3JiZnd0amx2Y2ZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMDAwMDksImV4cCI6MjA2NjY3NjAwOX0.L83td9BHYz7Z6vQke7CXPZrcOXcQ8FKg9duBL-fm644'
        );

        if (!supabaseClient) {
            throw new Error('Supabase客户端未初始化');
        }

        const { data, error } = await supabaseClient
            .from('customer_service_messages')
            .select('*')
            .eq('user_id', currentUser.id)
            .order('created_at', { ascending: true });

        if (error) {
            console.error('获取用户消息失败:', error);
            throw error;
        }

        console.log('获取到用户消息:', data);
        return data || [];

    } catch (error) {
        console.error('获取用户客服消息失败:', error);
        return [];
    }
}

// 标记用户消息为已读
async function markUserMessagesAsRead(userId) {
    try {
        if (!userId) {
            console.log('用户ID为空，无法标记消息已读');
            return [];
        }

        const supabaseClient = window.supabaseClient || window.supabase?.createClient?.(
            'https://snckktsqwrbfwtjlvcfr.supabase.co',
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNuY2trdHNxd3JiZnd0amx2Y2ZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMDAwMDksImV4cCI6MjA2NjY3NjAwOX0.L83td9BHYz7Z6vQke7CXPZrcOXcQ8FKg9duBL-fm644'
        );

        if (!supabaseClient) {
            throw new Error('Supabase客户端未初始化');
        }

        const { data, error } = await supabaseClient
            .from('customer_service_messages')
            .update({ is_read: true })
            .eq('user_id', userId)
            .eq('message_type', 'admin')
            .eq('is_read', false)
            .select();

        if (error) {
            console.error('标记消息已读失败:', error);
            // 不抛出错误，因为这不是关键功能
            return [];
        }

        console.log('成功标记消息已读:', data);
        return data || [];

    } catch (error) {
        console.error('标记用户消息已读失败:', error);
        return [];
    }
}

// 添加脉冲动画
const style = document.createElement('style');
style.textContent = `
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }
`;
document.head.appendChild(style);
