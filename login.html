<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录 - 安徽春晟机械有限公司</title>
    <meta name="description" content="安徽春晟机械有限公司用户登录页面">
    <meta name="keywords" content="用户登录,安徽春晟机械">
    
    <!-- 样式文件 -->
    <link href="pcstyle.css" rel="stylesheet" type="text/css">
    <link href="reset.css" rel="stylesheet" type="text/css">
    <link href="32_Pc_zh-CN.css" rel="stylesheet" type="text/css">
    <link href="css/custom.css" rel="stylesheet" type="text/css">
    <link href="css/customer-service-chat.css" rel="stylesheet" type="text/css">

    <!-- jQuery -->
    <script src="jquery-3.6.3.min.js"></script>

    <!-- 简化Supabase客户端 -->
    <script src="supabase-simple.js"></script>

    <!-- 统一认证系统 -->
    <script src="js/auth-system.js"></script>
</head>
<body>
    <!-- 头部区域 -->
    <header class="header-section">
        <!-- Logo区域 -->
        <div class="logo-section" style="background-color: rgb(37, 37, 37); padding: 10px 0;">
            <div class="container" style="width: 1200px; margin: 0 auto; display: flex; justify-content: space-between; align-items: center;">
                <div class="logo">
                    <a href="index.html">
                        <img src="16792504.png" alt="安徽春晟机械有限公司" style="height: 60px;">
                    </a>
                </div>
                <div class="user-actions">
                    <a href="login.html" class="login-btn" style="color: white; margin-right: 15px;">登录</a>
                    <a href="register.html" class="register-btn" style="background: #be131b; color: white; padding: 5px 15px;">注册</a>
                </div>
            </div>
        </div>
        
        <!-- 导航栏 -->
        <nav class="navigation" style="background-color: #F5F5F5; border-bottom: 1px solid #ddd;">
            <div class="container" style="width: 1200px; margin: 0 auto;">
                <ul class="nav-menu" style="display: flex; list-style: none; margin: 0; padding: 0;">
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="index.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">网站首页</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="about.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">关于我们</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="factory.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">企业展示</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="factory.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">工厂设备</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="products.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">产品中心</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="news.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">新闻中心</a>
                    </li>
                    <li style="flex: 1; text-align: center;">
                        <a href="contact.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">联系我们</a>
                    </li>
                </ul>
            </div>
        </nav>
    </header>

    <!-- 页面标题 -->
    <section class="page-header" style="background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('16719971.jpg') center/cover; padding: 80px 0; color: white; text-align: center;">
        <div class="container" style="width: 1200px; margin: 0 auto;">
            <h1 style="font-size: 48px; margin-bottom: 20px;">用户登录</h1>
            <p style="font-size: 18px;">登录您的账户，享受专业的产品服务</p>
        </div>
    </section>

    <!-- 登录表单区域 -->
    <section class="login-section" style="padding: 80px 0; background: #f8f9fa;">
        <div class="container" style="width: 1200px; margin: 0 auto;">
            <div class="login-container" style="max-width: 500px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 5px 20px rgba(0,0,0,0.1);">
                <div class="login-header" style="text-align: center; margin-bottom: 30px;">
                    <h2 style="color: #333; margin-bottom: 10px;">登录账户</h2>
                    <p style="color: #666;">请输入您的登录信息</p>
                </div>

                <form id="login-form" class="login-form">
                    <div class="form-group" style="margin-bottom: 20px;">
                        <label for="email" style="display: block; margin-bottom: 5px; color: #333; font-weight: bold;">邮箱地址</label>
                        <input type="email" id="email" name="email" required 
                               style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 5px; font-size: 16px; outline: none; transition: border-color 0.3s;"
                               placeholder="请输入您的邮箱地址">
                    </div>

                    <div class="form-group" style="margin-bottom: 20px;">
                        <label for="password" style="display: block; margin-bottom: 5px; color: #333; font-weight: bold;">密码</label>
                        <input type="password" id="password" name="password" required 
                               style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 5px; font-size: 16px; outline: none; transition: border-color 0.3s;"
                               placeholder="请输入您的密码">
                    </div>

                    <div class="form-group" style="margin-bottom: 30px; display: flex; justify-content: space-between; align-items: center;">
                        <label style="display: flex; align-items: center; gap: 5px; color: #666;">
                            <input type="checkbox" id="remember-me" name="remember">
                            <span>记住我</span>
                        </label>
                        <a href="#" style="color: #be131b; text-decoration: none; font-size: 14px;">忘记密码？</a>
                    </div>

                    <button type="submit" class="login-btn" 
                            style="width: 100%; padding: 15px; background: #be131b; color: white; border: none; border-radius: 5px; font-size: 16px; font-weight: bold; cursor: pointer; transition: background-color 0.3s;">
                        登录
                    </button>
                </form>

                <div class="login-footer" style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
                    <p style="color: #666; margin-bottom: 15px;">还没有账户？</p>
                    <a href="register.html" style="color: #be131b; text-decoration: none; font-weight: bold;">立即注册</a>
                </div>

                <!-- 登录状态提示 -->
                <div id="login-message" class="login-message" style="margin-top: 20px; padding: 10px; border-radius: 5px; text-align: center; display: none;"></div>
            </div>
        </div>
    </section>



    <!-- 页脚 -->
    <footer style="background: #333; color: white; padding: 40px 0;">
        <div class="container" style="width: 1200px; margin: 0 auto; text-align: center;">
            <p>&copy; 2024 安徽春晟机械有限公司. 保留所有权利.</p>
            <p>地址：安徽省广德经济开发区 | 专业从事减震器冲压件设计与生产</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // 等待认证系统初始化
        function waitForAuthSystem() {
            return new Promise((resolve) => {
                if (window.authSystem && window.authSystem.supabase) {
                    resolve();
                } else {
                    setTimeout(() => waitForAuthSystem().then(resolve), 100);
                }
            });
        }

        // 登录表单处理
        document.getElementById('login-form').addEventListener('submit', async function(e) {
            e.preventDefault();

            const email = document.getElementById('email').value.trim();
            const password = document.getElementById('password').value;

            if (!email || !password) {
                showMessage('请输入邮箱和密码', 'error');
                return;
            }

            // 显示加载状态
            showMessage('正在登录...', 'info');

            try {
                // 等待认证系统初始化
                await waitForAuthSystem();

                // 使用认证系统登录
                const result = await window.authSystem.loginUser(email, password);

                console.log('登录结果:', result);

                if (result.success) {
                    showMessage('登录成功！正在跳转...', 'success');

                    // 延迟跳转到首页
                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 1500);
                } else {
                    showMessage(result.message || '登录失败，请检查邮箱和密码', 'error');
                }

            } catch (error) {
                console.error('登录错误:', error);
                showMessage('登录失败，请稍后重试: ' + error.message, 'error');
            }
        });
        
        // 显示消息
        function showMessage(message, type) {
            const messageDiv = document.getElementById('login-message');
            messageDiv.textContent = message;
            messageDiv.style.display = 'block';
            
            // 根据类型设置样式
            messageDiv.className = 'login-message';
            switch(type) {
                case 'success':
                    messageDiv.style.backgroundColor = '#d4edda';
                    messageDiv.style.color = '#155724';
                    messageDiv.style.border = '1px solid #c3e6cb';
                    break;
                case 'error':
                    messageDiv.style.backgroundColor = '#f8d7da';
                    messageDiv.style.color = '#721c24';
                    messageDiv.style.border = '1px solid #f5c6cb';
                    break;
                case 'info':
                    messageDiv.style.backgroundColor = '#d1ecf1';
                    messageDiv.style.color = '#0c5460';
                    messageDiv.style.border = '1px solid #bee5eb';
                    break;
            }
        }
        
        // 输入框焦点效果
        document.querySelectorAll('input[type="email"], input[type="password"]').forEach(input => {
            input.addEventListener('focus', function() {
                this.style.borderColor = '#be131b';
            });
            
            input.addEventListener('blur', function() {
                this.style.borderColor = '#ddd';
            });
        });
        
        // 登录按钮悬停效果
        document.querySelector('.login-btn').addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#a00e15';
        });
        
        document.querySelector('.login-btn').addEventListener('mouseleave', function() {
            this.style.backgroundColor = '#be131b';
        });
    </script>

    <!-- 客服系统脚本 -->
    <script src="js/customer-service-chat.js"></script>
</body>
</html>
