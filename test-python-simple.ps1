# Simple Python test

Write-Host "Testing Python installation..." -ForegroundColor Green

# Test python command
Write-Host "`nTesting 'python --version':" -ForegroundColor Yellow
try {
    $output = & python --version 2>&1
    Write-Host "Success: $output" -ForegroundColor Green
} catch {
    Write-Host "Failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test pip command
Write-Host "`nTesting 'pip --version':" -ForegroundColor Yellow
try {
    $output = & pip --version 2>&1
    Write-Host "Success: $output" -ForegroundColor Green
} catch {
    Write-Host "Failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test HTTP server
Write-Host "`nTesting HTTP server (will run for 5 seconds):" -ForegroundColor Yellow
try {
    Write-Host "Starting server on port 8080..." -ForegroundColor Cyan
    $job = Start-Job -ScriptBlock { 
        Set-Location "d:\chunsheng-website"
        & python -m http.server 8080 
    }
    
    Start-Sleep -Seconds 2
    
    # Test if server is responding
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8080" -TimeoutSec 3
        Write-Host "Server is running! Status: $($response.StatusCode)" -ForegroundColor Green
    } catch {
        Write-Host "Server test failed: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # Stop the server
    Stop-Job $job
    Remove-Job $job
    Write-Host "Server stopped." -ForegroundColor Cyan
    
} catch {
    Write-Host "HTTP server test failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nPython installation test completed!" -ForegroundColor Green
