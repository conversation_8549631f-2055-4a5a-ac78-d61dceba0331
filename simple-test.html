<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单搜索测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; }
        .error { background: #f8d7da; }
        button { padding: 10px 20px; margin: 10px; background: #be131b; color: white; border: none; border-radius: 5px; cursor: pointer; }
        input { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>🔍 简单搜索测试</h1>
    
    <div>
        <input type="text" id="search-input" placeholder="输入搜索词，如：φ78, SPHC, 2.5" value="φ78">
        <button onclick="performTest()">搜索测试</button>
        <button onclick="showProducts()">显示产品数据</button>
    </div>
    
    <div id="results"></div>

    <!-- 引入必要的脚本 -->
    <script src="supabase-simple.js"></script>
    <script src="js/smart-search.js"></script>

    <script>
        let simpleSmartSearch;
        
        // 初始化
        async function init() {
            try {
                log('🔧 初始化搜索引擎...');
                
                simpleSmartSearch = new SmartSearch();
                
                // 从Supabase加载产品数据
                const { data: products, error } = await supabase
                    .from('products')
                    .select('*');
                
                if (error) throw error;
                
                simpleSmartSearch.setProducts(products);
                
                log(`✅ 初始化成功！加载了 ${products.length} 个产品`, 'success');
                
                // 显示第一个产品作为示例
                if (products.length > 0) {
                    const sample = products[0];
                    log(`📋 产品示例: ${sample.data_id} - ${sample.product_name} (${sample.specifications}, ${sample.material})`);
                }
                
            } catch (error) {
                log(`❌ 初始化失败: ${error.message}`, 'error');
            }
        }

        function log(message, type = '') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
            console.log(message);
        }

        function showProducts() {
            if (!simpleSmartSearch) {
                log('❌ 搜索引擎未初始化', 'error');
                return;
            }

            const products = simpleSmartSearch.products;
            log(`📊 产品列表 (共 ${products.length} 个):`);
            
            products.slice(0, 5).forEach(product => {
                log(`  • ${product.data_id} - ${product.product_name}`);
                log(`    规格: ${product.specifications || '无'}, 材料: ${product.material || '无'}, 厚度: ${product.thickness || '无'}`);
            });
            
            if (products.length > 5) {
                log(`  ... 还有 ${products.length - 5} 个产品`);
            }
        }

        function performTest() {
            if (!simpleSmartSearch) {
                log('❌ 搜索引擎未初始化', 'error');
                return;
            }

            const query = document.getElementById('search-input').value.trim();
            if (!query) {
                log('❌ 请输入搜索词', 'error');
                return;
            }

            log(`🔍 搜索: "${query}"`);
            
            try {
                // 测试字符串标准化
                const normalized = simpleSmartSearch.normalizeQuery(query);
                log(`🔧 标准化后: "${normalized}"`);
                
                // 执行搜索
                const results = simpleSmartSearch.search(query, {
                    tolerance: 3,
                    includeApproximate: true
                });
                
                log(`📊 搜索结果:`);
                log(`  精确匹配: ${results.exact.length} 个`);
                log(`  近似匹配: ${results.approximate.length} 个`);
                
                // 显示精确匹配
                if (results.exact.length > 0) {
                    log(`✅ 精确匹配结果:`, 'success');
                    results.exact.forEach(product => {
                        log(`  • ${product.data_id} - ${product.product_name} (${product.specifications}, ${product.material})`);
                    });
                }
                
                // 显示近似匹配
                if (results.approximate.length > 0) {
                    log(`≈ 近似匹配结果:`);
                    results.approximate.forEach(product => {
                        log(`  • ${product.data_id} - ${product.product_name} (${product.specifications}, ${product.material})`);
                    });
                }
                
                if (results.exact.length === 0 && results.approximate.length === 0) {
                    log(`❌ 没有找到匹配的产品`, 'error');
                    
                    // 手动检查匹配
                    log(`🔍 手动检查匹配:`);
                    const products = simpleSmartSearch.products;
                    let found = false;
                    
                    products.forEach(product => {
                        const fields = [product.specifications, product.material, product.product_name];
                        fields.forEach(field => {
                            if (field && field.includes(query)) {
                                log(`  ✓ 找到包含 "${query}" 的字段: ${product.data_id} - ${field}`);
                                found = true;
                            }
                        });
                    });
                    
                    if (!found) {
                        log(`  ❌ 手动检查也没有找到包含 "${query}" 的字段`);
                    }
                }
                
            } catch (error) {
                log(`❌ 搜索失败: ${error.message}`, 'error');
                console.error('搜索错误:', error);
            }
        }

        // 页面加载完成后初始化
        window.addEventListener('load', init);
    </script>
</body>
</html>
