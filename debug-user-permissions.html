<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户权限调试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            margin: 40px;
            line-height: 1.6;
            background: #f5f5f5;
        }
        .debug-panel {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            background: #007bff;
            color: white;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>🔍 用户权限调试面板</h1>
    
    <div class="debug-panel">
        <h2>当前用户状态</h2>
        <div id="user-status">加载中...</div>
        <button onclick="refreshUserStatus()">🔄 刷新状态</button>
        <button onclick="clearAllAuth()">🗑️ 清除所有认证</button>
    </div>

    <div class="debug-panel">
        <h2>权限检查结果</h2>
        <div id="permission-status">检查中...</div>
    </div>

    <div class="debug-panel">
        <h2>全局变量状态</h2>
        <div id="global-vars">检查中...</div>
    </div>

    <div class="debug-panel">
        <h2>认证管理器状态</h2>
        <div id="auth-manager-status">检查中...</div>
    </div>

    <div class="debug-panel">
        <h2>本地存储状态</h2>
        <div id="local-storage-status">检查中...</div>
    </div>

    <script src="supabase-simple.js"></script>
    <script src="js/auth-system.js"></script>
    
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', async function() {
            // 等待认证系统初始化
            await new Promise(resolve => setTimeout(resolve, 2000));

            refreshUserStatus();
            setInterval(refreshUserStatus, 5000); // 每5秒刷新一次
        });

        function refreshUserStatus() {
            checkUserStatus();
            checkPermissions();
            checkGlobalVars();
            checkAuthManager();
            checkLocalStorage();
        }

        function checkUserStatus() {
            const statusDiv = document.getElementById('user-status');
            let html = '';

            // 检查当前用户
            if (typeof currentUser !== 'undefined' && currentUser) {
                html += `<div class="status success">
                    <strong>✅ 用户已登录</strong><br>
                    用户名: ${currentUser.username || currentUser.email}<br>
                    邮箱: ${currentUser.email}<br>
                    用户类型: ${currentUser.user_type}<br>
                    ID: ${currentUser.id}
                </div>`;
            } else {
                html += `<div class="status error">
                    <strong>❌ 用户未登录</strong><br>
                    currentUser: ${typeof currentUser} = ${currentUser}
                </div>`;
            }

            // 检查用户类型
            if (typeof currentUserType !== 'undefined') {
                html += `<div class="status info">
                    <strong>当前用户类型:</strong> ${currentUserType}<br>
                    <strong>类型显示:</strong> ${typeof getUserTypeDisplay === 'function' ? getUserTypeDisplay() : '函数不存在'}
                </div>`;
            } else {
                html += `<div class="status error">
                    <strong>❌ currentUserType 未定义</strong>
                </div>`;
            }

            statusDiv.innerHTML = html;
        }

        function checkPermissions() {
            const statusDiv = document.getElementById('permission-status');
            let html = '';

            try {
                const permissions = {
                    canViewDetails: typeof canViewDetails === 'function' ? canViewDetails() : '函数不存在',
                    canDownload: typeof canDownload === 'function' ? canDownload() : '函数不存在',
                    canDownloadBasic: typeof canDownloadBasic === 'function' ? canDownloadBasic() : '函数不存在'
                };

                html += `<div class="status info">
                    <strong>权限检查结果:</strong><br>
                    查看详情: ${permissions.canViewDetails ? '✅' : '❌'} ${permissions.canViewDetails}<br>
                    下载PDF: ${permissions.canDownload ? '✅' : '❌'} ${permissions.canDownload}<br>
                    下载基础资料: ${permissions.canDownloadBasic ? '✅' : '❌'} ${permissions.canDownloadBasic}
                </div>`;
            } catch (error) {
                html += `<div class="status error">
                    <strong>❌ 权限检查出错:</strong><br>
                    ${error.message}
                </div>`;
            }

            statusDiv.innerHTML = html;
        }

        function checkGlobalVars() {
            const statusDiv = document.getElementById('global-vars');
            
            const vars = {
                currentUser: typeof currentUser,
                currentUserType: typeof currentUserType,
                USER_TYPES: typeof USER_TYPES,
                supabase: typeof supabase,
                canViewDetails: typeof canViewDetails,
                canDownload: typeof canDownload,
                canDownloadBasic: typeof canDownloadBasic
            };

            let html = '<pre>' + JSON.stringify(vars, null, 2) + '</pre>';
            
            if (typeof USER_TYPES !== 'undefined') {
                html += '<pre>USER_TYPES: ' + JSON.stringify(USER_TYPES, null, 2) + '</pre>';
            }

            statusDiv.innerHTML = html;
        }

        function checkAuthManager() {
            const statusDiv = document.getElementById('auth-manager-status');
            let html = '';

            if (typeof window.authManager !== 'undefined') {
                const authManager = window.authManager;
                html += `<div class="status success">
                    <strong>✅ AuthManager 存在</strong><br>
                    当前用户: ${authManager.getCurrentUser() ? '已登录' : '未登录'}<br>
                    用户类型: ${authManager.getUserType()}<br>
                    是否管理员: ${authManager.isAdmin()}<br>
                    是否传统管理员: ${authManager.isLegacyAdminUser()}
                </div>`;
            } else {
                html += `<div class="status error">
                    <strong>❌ AuthManager 不存在</strong>
                </div>`;
            }

            statusDiv.innerHTML = html;
        }

        function checkLocalStorage() {
            const statusDiv = document.getElementById('local-storage-status');
            
            const adminKeys = [
                'admin_logged_in',
                'admin_username', 
                'admin_user_id',
                'admin_email',
                'admin_login_time'
            ];

            let html = '<h4>管理员登录状态:</h4>';
            adminKeys.forEach(key => {
                const value = localStorage.getItem(key);
                html += `<div>${key}: ${value || '未设置'}</div>`;
            });

            statusDiv.innerHTML = html;
        }

        function clearAllAuth() {
            if (confirm('确定要清除所有认证信息吗？')) {
                // 清除本地存储
                localStorage.removeItem('admin_logged_in');
                localStorage.removeItem('admin_username');
                localStorage.removeItem('admin_user_id');
                localStorage.removeItem('admin_email');
                localStorage.removeItem('admin_login_time');
                
                // 清除Supabase认证
                if (typeof supabase !== 'undefined') {
                    supabase.auth.signOut();
                }
                
                // 重置全局变量
                if (typeof window.authManager !== 'undefined') {
                    window.authManager.logout();
                }
                
                alert('所有认证信息已清除，请刷新页面');
                location.reload();
            }
        }
    </script>
</body>
</html>
