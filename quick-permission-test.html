<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速权限测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            margin: 40px;
            line-height: 1.6;
        }
        .result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 5px solid;
        }
        .success { background: #d4edda; border-color: #28a745; color: #155724; }
        .error { background: #f8d7da; border-color: #dc3545; color: #721c24; }
        .info { background: #d1ecf1; border-color: #17a2b8; color: #0c5460; }
    </style>
</head>
<body>
    <h1>🚀 快速权限测试</h1>
    <div id="results">加载中...</div>

    <script src="supabase-simple.js"></script>
    <script src="js/auth-system.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', async function() {
            // 等待初始化
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            const resultsDiv = document.getElementById('results');
            let html = '';
            
            // 检查AuthSystem
            if (typeof window.authSystem !== 'undefined') {
                const authUser = window.authSystem.getCurrentUser();
                const authUserType = window.authSystem.getUserType();

                html += `<div class="result info">
                    <strong>AuthSystem状态:</strong><br>
                    用户: ${authUser ? authUser.email : '无'}<br>
                    类型: ${authUserType}<br>
                    是管理员: ${window.authSystem.isAdmin()}
                </div>`;
            }
            
            // 检查全局变量
            html += `<div class="result info">
                <strong>全局变量状态:</strong><br>
                currentUser: ${currentUser ? currentUser.username : 'null'}<br>
                currentUserType: ${currentUserType}<br>
                USER_TYPES.ADMIN: ${USER_TYPES.ADMIN}
            </div>`;
            
            // 检查权限
            try {
                const permissions = {
                    canViewDetails: canViewDetails(),
                    canDownload: canDownload(),
                    canDownloadBasic: canDownloadBasic()
                };
                
                html += `<div class="result ${permissions.canDownload ? 'success' : 'error'}">
                    <strong>权限测试结果:</strong><br>
                    查看详情: ${permissions.canViewDetails ? '✅' : '❌'}<br>
                    下载PDF: ${permissions.canDownload ? '✅' : '❌'}<br>
                    下载基础资料: ${permissions.canDownloadBasic ? '✅' : '❌'}
                </div>`;
                
                if (permissions.canDownload) {
                    html += `<div class="result success">
                        <strong>🎉 权限正常！</strong><br>
                        您现在可以访问产品页面和下载PDF了。
                    </div>`;
                } else {
                    html += `<div class="result error">
                        <strong>❌ 权限不足</strong><br>
                        需要手动同步权限状态。
                    </div>`;
                    
                    // 手动同步权限
                    if (typeof window.authManager !== 'undefined') {
                        const authUser = window.authManager.getCurrentUser();
                        const authUserType = window.authManager.getUserType();
                        
                        if (authUser && authUserType !== 'guest') {
                            window.currentUser = authUser;
                            window.currentUserType = authUserType;
                            
                            html += `<div class="result info">
                                <strong>🔧 已手动同步权限</strong><br>
                                请刷新页面查看效果。
                            </div>`;
                        }
                    }
                }
                
            } catch (error) {
                html += `<div class="result error">
                    <strong>权限检查出错:</strong><br>
                    ${error.message}
                </div>`;
            }
            
            resultsDiv.innerHTML = html;
        });
    </script>
</body>
</html>
