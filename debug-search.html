<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索调试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .error { background: #f8d7da; color: #721c24; }
        .success { background: #d4edda; color: #155724; }
        button { padding: 8px 15px; margin: 5px; background: #be131b; color: white; border: none; border-radius: 3px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🔍 搜索功能调试</h1>
    
    <button onclick="debugBasicSearch()">调试基础搜索</button>
    <button onclick="debugNormalization()">调试字符串标准化</button>
    <button onclick="debugProductData()">调试产品数据</button>
    
    <div id="debug-output"></div>

    <!-- 引入必要的脚本 -->
    <script src="supabase-simple.js"></script>
    <script src="js/smart-search.js"></script>

    <script>
        let debugSmartSearch;
        
        // 初始化
        async function init() {
            try {
                console.log('初始化调试环境...');
                
                debugSmartSearch = new SmartSearch();
                
                // 从Supabase加载产品数据
                const { data: products, error } = await supabase
                    .from('products')
                    .select('*');
                
                if (error) throw error;
                
                debugSmartSearch.setProducts(products);
                
                log(`✅ 初始化成功，加载了 ${products.length} 个产品`);
                
            } catch (error) {
                log(`❌ 初始化失败: ${error.message}`, 'error');
            }
        }

        function log(message, type = 'debug') {
            const output = document.getElementById('debug-output');
            const div = document.createElement('div');
            div.className = `debug ${type}`;
            div.innerHTML = message;
            output.appendChild(div);
            console.log(message);
        }

        function debugProductData() {
            if (!debugSmartSearch) {
                log('❌ 搜索引擎未初始化', 'error');
                return;
            }

            const products = debugSmartSearch.products;
            log(`📊 产品总数: ${products.length}`);
            
            // 显示前3个产品的详细信息
            products.slice(0, 3).forEach((product, index) => {
                log(`产品 ${index + 1}:`);
                log(`  - ID: ${product.data_id}`);
                log(`  - 名称: ${product.product_name}`);
                log(`  - 规格: ${product.specifications}`);
                log(`  - 材料: ${product.material}`);
                log(`  - 厚度: ${product.thickness}`);
            });
        }

        function debugNormalization() {
            if (!debugSmartSearch) {
                log('❌ 搜索引擎未初始化', 'error');
                return;
            }

            const testQueries = ['φ78', 'SPHC', '2.5', 'phi78'];
            
            log('🔧 字符串标准化测试:');
            testQueries.forEach(query => {
                const normalized = debugSmartSearch.normalizeQuery(query);
                log(`  "${query}" → "${normalized}"`);
            });
        }

        function debugBasicSearch() {
            if (!debugSmartSearch) {
                log('❌ 搜索引擎未初始化', 'error');
                return;
            }

            const testQuery = 'φ78';
            log(`🔍 搜索测试: "${testQuery}"`);
            
            // 手动检查匹配
            const normalizedQuery = debugSmartSearch.normalizeQuery(testQuery);
            log(`标准化查询: "${normalizedQuery}"`);
            
            const products = debugSmartSearch.products;
            let manualMatches = [];
            
            products.forEach(product => {
                const fields = [product.specifications, product.material, product.product_name];
                fields.forEach(field => {
                    if (field) {
                        const normalizedField = debugSmartSearch.normalizeQuery(field);
                        if (normalizedField.includes(normalizedQuery)) {
                            manualMatches.push({
                                product: product.data_id + ' - ' + product.product_name,
                                field: field,
                                normalized: normalizedField,
                                match: true
                            });
                        }
                    }
                });
            });
            
            log(`手动匹配结果: ${manualMatches.length} 个`);
            manualMatches.forEach(match => {
                log(`  ✓ ${match.product}: "${match.field}" → "${match.normalized}"`);
            });
            
            // 使用智能搜索
            const results = debugSmartSearch.search(testQuery, {
                tolerance: 3,
                includeApproximate: true
            });
            
            log(`智能搜索结果:`);
            log(`  精确匹配: ${results.exact.length} 个`);
            log(`  近似匹配: ${results.approximate.length} 个`);
            
            results.exact.forEach(product => {
                log(`  ✓ 精确: ${product.data_id} - ${product.product_name}`);
            });
            
            results.approximate.forEach(product => {
                log(`  ≈ 近似: ${product.data_id} - ${product.product_name}`);
            });
        }

        // 页面加载完成后初始化
        window.addEventListener('load', init);
    </script>
</body>
</html>
